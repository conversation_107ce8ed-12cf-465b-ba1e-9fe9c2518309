# Importing the libraries

import cv2 
from deepface import DeepFace
import matplotlib.pyplot as plt

# load the image
image = cv2.imread(r"C:\Users\<USER>\PycharmProjects\attendanceproj\imageDatabase\002966.jpg")
# Displaying the image 
plt.axis("off")
plt.imshow(image)

# By default cv2 displays images in BGR(Blue,Green,Red) format. We are converting it to RGB.

# Displaying the image 
plt.axis("off")
plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))

# Loading the pre-trained DeepFace model

predictions = DeepFace.analyze(image)

predictions


# To detect the faces 

# Load some pre-trained data on face frontals from Opencv (haar cascade algorithm)
#The haarcascade_frontalface_default. xml is a haar cascade designed by OpenCV to detect the frontal face.
trained_face_data = cv2.CascadeClassifier(r"D:\VIT\S7\Image and Video Analytics\j comp\IVA\haarcascade_frontalface_default.xml")

# Must convert to grayscale
grayscaled_img = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

# Detect faces
face_coordinates = trained_face_data.detectMultiScale(grayscaled_img)

print(face_coordinates)

# Draw rectangle around the faces
length = len(face_coordinates)
for i in range(length):
    (x,y,w,h) = face_coordinates[i]
    cv2.rectangle(image, (x,y), (x+w,y+h), (0,255,0), 8)

# Displaying the image 
plt.axis("off")
plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))

import cv2
import matplotlib.pyplot as plt

# Replace this with the actual index of the dominant emotion
dominant_emotion_index = 0

# Load your image using cv2.imread
image = cv2.imread(r"C:\Users\<USER>\PycharmProjects\attendanceproj\imageDatabase\002966.jpg")

# Define the font and color for text
font = cv2.FONT_HERSHEY_SIMPLEX
font_scale = 1
font_color = (0, 0, 255)  # BGR color (red in this case)
font_thickness = 2
font_line_type = cv2.LINE_AA

# Get the dominant emotion from your predictions
dominant_emotion = predictions[dominant_emotion_index]['dominant_emotion']

# Position to display the text
position = (0, 50)

# Add the text to the image
cv2.putText(image, dominant_emotion, position, font, font_scale, font_color, font_thickness, font_line_type)

# Display the image
plt.axis("off")
plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))

import cv2
import numpy as np
from tensorflow.keras.preprocessing.image import img_to_array
from tensorflow.keras.models import load_model

# Load the emotion detection model
emotion_model = load_model(r"C:\Users\<USER>\PycharmProjects\attendanceproj\imageDatabase\fer2013_mini_XCEPTION.102-0.66.hdf5")

# Labels for different emotions
emotion_labels = ['Angry', 'Disgust', 'Fear', 'Happy', 'Sad', 'Surprise', 'Neutral']

# Load an image
img = cv2.imread(r"C:\Users\<USER>\PycharmProjects\attendanceproj\imageDatabase\002966.jpg")
img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)  # Convert to grayscale
img = cv2.resize(img, (64, 64))
img = img_to_array(img) / 255.0  # Normalize

# Predict emotion
emotion_prediction = emotion_model.predict(np.expand_dims(img, axis=0))

# Get the label of the predicted emotion
predicted_emotion = emotion_labels[np.argmax(emotion_prediction)]

print(f"Dominant Emotion: {predicted_emotion}")


import cv2
import matplotlib.pyplot as plt
from sklearn.metrics import accuracy_score
#import cv2
import numpy as np
from tensorflow.keras.preprocessing.image import img_to_array
from tensorflow.keras.models import load_model

# Your existing code for displaying the dominant emotion on an image
# Load the emotion detection model
emotion_model = load_model(r"C:\Users\<USER>\PycharmProjects\attendanceproj\imageDatabase\fer2013_mini_XCEPTION.102-0.66.hdf5")

# Labels for different emotions
emotion_labels = ['Angry', 'Disgust', 'Fear', 'Happy', 'Sad', 'Surprise', 'Neutral']

# Load an image
img = cv2.imread(r"C:\Users\<USER>\PycharmProjects\attendanceproj\imageDatabase\002966.jpg")
img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)  # Convert to grayscale
img = cv2.resize(img, (64, 64))
img = img_to_array(img) / 255.0  # Normalize

# Predict emotion
emotion_prediction = emotion_model.predict(np.expand_dims(img, axis=0))


# Replace this with your actual predictions and true emotion labels
predictions = [{'dominant_emotion': 'Happy'}, {'dominant_emotion': 'Sad'}, {'dominant_emotion': 'Angry'},{'dominant_emotion': 'Disgust'},{'dominant_emotion': 'Neutral'},{'dominant_emotion': 'Surprise'}, {'dominant_emotion': 'Fear'}]
true_emotion_labels = ['Happy', 'Sad','Angry','Neutral','Fear', 'Surprise', 'Disgust']

# Calculate accuracy by comparing predictions with true labels
predicted_emotions = [p['dominant_emotion'] for p in predictions]
accuracy = accuracy_score(true_emotion_labels, predicted_emotions)

print(f"Accuracy: {accuracy * 100:.2f}%")

# Display the image with the dominant emotion
plt.axis("off")
plt.imshow(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))


import cv2
import matplotlib.pyplot as plt
import numpy as np
from tensorflow.keras.preprocessing.image import img_to_array
from tensorflow.keras.models import load_model

# Load the emotion detection model
emotion_model = load_model(r"C:\Users\<USER>\PycharmProjects\attendanceproj\imageDatabase\fer2013_mini_XCEPTION.102-0.66.hdf5")

# Labels for different emotions
emotion_labels = ['Angry', 'Disgust', 'Fear', 'Happy', 'Sad', 'Surprise', 'Neutral']

# Load an image
img = cv2.imread(r"C:\Users\<USER>\PycharmProjects\attendanceproj\imageDatabase\002966.jpg")
img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)  # Convert to grayscale
img = cv2.resize(img, (64, 64))
img = img_to_array(img) / 255.0  # Normalize

# Predict emotion
emotion_prediction = emotion_model.predict(np.expand_dims(img, axis=0))

# Get the label of the predicted emotion
predicted_emotion_label = emotion_labels[np.argmax(emotion_prediction)]
predicted_emotion_score = emotion_prediction[0][np.argmax(emotion_prediction)]

# Display the image with the dominant emotion
font = cv2.FONT_HERSHEY_DUPLEX

# Adjust the font_scale to make the text smaller (e.g., 0.5)
font_scale = 0.5

font_color = (255, 255, 255)  # BGR color (white in this case)

# Adjust the font_thickness to make the text less bold (e.g., 1)
font_thickness = 1

font_line_type = cv2.LINE_AA

text = f"{predicted_emotion_label} ({predicted_emotion_score:.2f})"
position = (5, 10)

cv2.putText(img, text, position, font, font_scale, font_color, font_thickness, font_line_type)

# Convert the image to RGB format for displaying with matplotlib
img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

# Display the image with the predicted emotion
plt.axis("off")
plt.imshow(img_rgb)
plt.show()


# face emotion detection in live camera

import cv2
from deepface import DeepFace

face_cascade = cv2.CascadeClassifier('haarcascade_frontalface_default.xml')

cap = cv2.VideoCapture(0)

while True:
    ret, frame = cap.read()
    result = DeepFace.analyze(img_path=frame, actions=['emotion'], enforce_detection=False)

    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

    faces = face_cascade.detectMultiScale(gray, 1.1, 4)

    for (x, y, w, h) in faces:
        cv2.rectangle(frame, (x, y), (x + w, y + h), (255, 0, 0), 3)

        # Assuming result is a list of dictionaries
        # Accessing the first dictionary in the list
        first_dict = result[0]

        # Accessing the "dominant_emotion" key in the first dictionary
        emotion = first_dict["dominant_emotion"]

    #emotion = result["dominant_emotion"]

    txt = str(emotion)

    cv2.putText(frame, txt, (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 3)
    cv2.imshow('frame', frame)

    if cv2.waitKey(1) & 0xff == ord('q'):
        break

cap.release()
cv2.destroyAllWindows()